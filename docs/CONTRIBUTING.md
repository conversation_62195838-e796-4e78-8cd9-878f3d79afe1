npm run lint           # Fix linting issues
npm run typecheck      # Fix typecheck errors
npm run test           # Run unit tests
npm run doctor         # Validate native dependencies for Expo
npm run test:ci        # Run all of the above quality checks at once
npm start              # Start development server

git checkout main
git pull origin main
git checkout -b feature-name

npm run test:ci  # Must pass before creating PR

type: Brief description of change

Examples:
fix: Fix steps syncing bug
feat: Add native health data integration
docs: Update API documentation
test: Add unit tests for challenge logic

# Ensure your branch is up to date
git checkout main && git pull
git checkout your-branch
git rebase main

npm update                 # Update to latest patch versions
npx expo install --fix     # Sync Expo dependencies

npx npm-check -u          # Interactive update tool

npm i -g eas-cli                    # Update EAS CLI
npm install expo@^52.0.0           # Update to new Expo version
npx expo install --fix             # Sync all Expo dependencies
npx expo-doctor                    # Validate configuration

# See all debug logs from app
adb logcat -c
adb logcat | grep "com.flybodies.flyfitdev"

# Uninstall app from device/emulator
adb uninstall com.flybodies.flyfitdev

# Reset app permissions
adb shell pm reset-permissions com.flybodies.flyfitdev

# Start emulator in background
nohup emulator -avd Pixel_9_API_35 &

curl https://get.flashlight.dev | bash

flashlight measure