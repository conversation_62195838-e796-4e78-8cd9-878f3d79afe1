{"$schema": "https://json.schemastore.org/tsconfig", "compileOnSave": true, "compilerOptions": {"baseUrl": "./", "module": "esnext", "moduleResolution": "bundler", "moduleDetection": "force", "jsx": "react-native", "target": "ES2022", "strict": true, "alwaysStrict": true, "allowJs": true, "maxNodeModuleJsDepth": 0, "allowSyntheticDefaultImports": true, "allowUnreachableCode": false, "allowUnusedLabels": false, "incremental": true, "forceConsistentCasingInFileNames": true, "esModuleInterop": true, "isolatedModules": true, "noFallthroughCasesInSwitch": true, "noUnusedLocals": false, "noUnusedParameters": false, "strictPropertyInitialization": true, "noUncheckedIndexedAccess": true, "noImplicitAny": true, "noImplicitOverride": false, "noImplicitThis": true, "noImplicitReturns": false, "noErrorTruncation": true, "noPropertyAccessFromIndexSignature": false, "resolveJsonModule": true, "noEmit": true, "removeComments": true, "exactOptionalPropertyTypes": true, "skipDefaultLibCheck": true, "skipLibCheck": true, "strictNullChecks": true, "verbatimModuleSyntax": true, "assumeChangesOnlyAffectDirectDependencies": true, "allowUmdGlobalAccess": true, "typeRoots": ["./src/types", "./node_modules/@types"], "lib": ["ESNext", "DOM"], "paths": {"@env": ["./src/types/env.d.ts"], "@api": ["./src/api/index.ts"], "@assets": ["./src/assets/index.ts"], "@pages": ["./src/pages/index.ts"], "@contexts": ["./src/contexts/index.ts"], "@components": ["./src/components/index.ts"], "@base-components": ["./src/base-components/index.ts"], "@backend": ["./src/utils/firestore/firebase.config.ts"], "@navigation": ["./src/utils/navigation/index.ts"], "@constants": ["./src/constants/index.ts"], "@hooks": ["./src/hooks/index.ts"], "@data-hooks": ["./src/data-hooks/index.ts"], "@types": ["./src/types/index.ts"], "@utils": ["./src/utils/index.ts"]}}, "include": ["src", "scripts", "functions", "web-sign-up", ".eslintrc.cjs", "*.config.ts", "index.js", "**/*.mjs", "**/*.config.cjs"], "exclude": ["**/node_modules", "**/dist", "**/lib", "dist-dev", "dist-prod"], "extends": "expo/tsconfig.base"}